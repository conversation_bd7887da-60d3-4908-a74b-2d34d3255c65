// 移动版
import React, { useState, useMemo, useEffect } from 'react';
import { Popup, Ellipsis } from '@blmcp/ui-mobile';
import useComponent from '@/pages/lego/hooks/useComponent';
import '@blmcp/ui-mobile/dist/es/style/theme-default.css';
import { queryDatasetUpdateTime } from '@/pages/lego/api';
export default (props: any) => {
  const [meta] = useComponent(props.componentId);
  const [visible, setVisible] = useState(false);
  const title = props.title || meta.title;
  const [updateTime, setUpdateTime] = useState('--');
  const dataSourceId = props?.dataSetConfig?.dataSourceId; // 获取指标数据
  const getData = () => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve({ data: '2025-08-08' });
      }, 300);
    });
  };
  // 获取指标数据
  useEffect(() => {
    if (dataSourceId) {
      queryDatasetUpdateTime({ datasetId: dataSourceId })
        .then((res: any) => {
          setUpdateTime(res.data?.updateTime);
        })
        .catch(() => {
          setUpdateTime('--');
        });
    }
  }, [dataSourceId]);
  const textValue = useMemo(() => {
    const newHtml = props.text?.replace(/\[数据更新时间\]/g, `${updateTime}`);
    return newHtml || '';
  }, [props.text, updateTime]);
  return (
    <div
      className="lego-text-wrap lego-bi-scroll-hide mobile"
      onClick={() => setVisible(true)}
      style={{ minHeight: '20px' }}
    >
      {/* <Ellipsis content={textValue} rows={3} direction="end"></Ellipsis> */}
      <div
        dangerouslySetInnerHTML={{ __html: textValue }}
        style={{ height: '60px', overflowY: 'hidden', fontSize: '14px' }}
      ></div>
      <Popup
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
        }}
        className="lego-text-popup-wrapper"
        onMaskClick={() => {
          setVisible(false);
        }}
        visible={visible}
      >
        <div className="lego-text-popup-header">{title}</div>
        <div
          className="lego-text-popup-content"
          dangerouslySetInnerHTML={{ __html: textValue }}
        ></div>
      </Popup>
    </div>
  );
};
